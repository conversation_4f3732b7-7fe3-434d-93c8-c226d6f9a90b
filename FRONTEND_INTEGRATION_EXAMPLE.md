# Frontend Integration Example

Here's how your frontend should handle the Google Calendar integration activation:

## 1. Connect <PERSON>ton Handler

```typescript
const handleGoogleCalendarConnect = async () => {
  try {
    // Get OAuth URL from backend
    const response = await fetch('/workspace/google-calendar/auth-url', {
      headers: {
        'Authorization': `<PERSON><PERSON> ${userToken}`
      }
    });
    
    const data = await response.json();
    
    // Redirect user to Google OAuth
    window.location.href = data.auth_url;
  } catch (error) {
    console.error('Failed to initiate Google Calendar OAuth:', error);
    // Show error message to user
  }
};
```

## 2. Integration Page Component

```typescript
import { useEffect, useState } from 'react';
import { useSearchParams } from 'react-router-dom';

const IntegrationsPage = () => {
  const [searchParams] = useSearchParams();
  const [integrations, setIntegrations] = useState([]);
  const [message, setMessage] = useState('');

  useEffect(() => {
    // Check for OAuth callback status
    const googleCalendarStatus = searchParams.get('google_calendar');
    
    if (googleCalendarStatus === 'success') {
      setMessage('Google Calendar connected successfully!');
      // Refresh integrations list
      fetchIntegrations();
      // Clear URL parameters
      window.history.replaceState({}, '', '/integrations');
    } else if (googleCalendarStatus === 'error') {
      setMessage('Failed to connect Google Calendar. Please try again.');
      // Clear URL parameters
      window.history.replaceState({}, '', '/integrations');
    }
  }, [searchParams]);

  const fetchIntegrations = async () => {
    try {
      const response = await fetch('/workspace/integrations', {
        headers: {
          'Authorization': `Bearer ${userToken}`
        }
      });
      const data = await response.json();
      setIntegrations(data);
    } catch (error) {
      console.error('Failed to fetch integrations:', error);
    }
  };

  return (
    <div>
      {message && (
        <div className={`alert ${googleCalendarStatus === 'success' ? 'alert-success' : 'alert-error'}`}>
          {message}
        </div>
      )}
      
      {/* Render integrations */}
      <div className="integrations-grid">
        {integrations.available_integrations?.map(integration => (
          <IntegrationCard 
            key={integration.source}
            integration={integration}
            onConnect={integration.source === 'GOOGLE_CALENDAR' ? handleGoogleCalendarConnect : undefined}
          />
        ))}
      </div>
    </div>
  );
};
```

## 3. Integration Card Component

```typescript
interface Integration {
  source: string;
  name: string;
  description: string;
  is_active: boolean;
}

const IntegrationCard = ({ integration, onConnect }) => {
  return (
    <div className="integration-card">
      <h3>{integration.name}</h3>
      <p>{integration.description}</p>
      
      {integration.is_active ? (
        <button className="btn-active" disabled>
          Active
        </button>
      ) : (
        <button 
          className="btn-connect" 
          onClick={onConnect}
        >
          Connect
        </button>
      )}
    </div>
  );
};
```

## 4. Error Handling

```typescript
// Add error boundary or global error handler
const handleOAuthError = (error) => {
  console.error('OAuth error:', error);
  
  // Show user-friendly error message
  toast.error('Failed to connect integration. Please try again.');
  
  // Optionally redirect back to integrations page
  navigate('/integrations');
};
```

## 5. Loading States

```typescript
const [isConnecting, setIsConnecting] = useState(false);

const handleGoogleCalendarConnect = async () => {
  setIsConnecting(true);
  
  try {
    const response = await fetch('/workspace/google-calendar/auth-url', {
      headers: { 'Authorization': `Bearer ${userToken}` }
    });
    
    const data = await response.json();
    window.location.href = data.auth_url;
  } catch (error) {
    console.error('Failed to initiate OAuth:', error);
    setIsConnecting(false);
  }
};

// In render:
<button 
  className="btn-connect" 
  onClick={handleGoogleCalendarConnect}
  disabled={isConnecting}
>
  {isConnecting ? 'Connecting...' : 'Connect'}
</button>
```

## Key Points

1. **OAuth Flow**: The backend handles all OAuth complexity
2. **Redirects**: User will leave your app briefly for Google auth
3. **Status Handling**: Check URL parameters on return for success/error
4. **Refresh Data**: Always refresh integrations list after successful connection
5. **Error Recovery**: Provide clear error messages and retry options
