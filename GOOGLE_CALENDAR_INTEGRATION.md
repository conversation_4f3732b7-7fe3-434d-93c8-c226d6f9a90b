# Google Calendar Integration Activation

This document explains how the Google Calendar integration activation works for users in the frontend.

## Overview

The Google Calendar integration allows users to connect their Google Calendar account to sync events and meetings. The integration follows the OAuth 2.0 PKCE flow for secure authentication.

## Integration Flow

### 1. Frontend Display
- The integration appears in the "Available integrations" section when:
  - Google Calendar is configured for the organization (has an `IntegrationConfig` record)
  - The user hasn't activated it yet (no `IntegrationUser` record exists)

### 2. User Clicks "Connect"
When a user clicks the "Connect" button on the Google Calendar integration:

1. **Frontend calls**: `GET /workspace/google-calendar/auth-url`
2. **Backend returns**: `{"auth_url": "https://accounts.google.com/oauth/authorize?..."}`
3. **Frontend redirects**: User's browser to the Google OAuth URL

### 3. Google OAuth Flow
1. User authenticates with Google
2. User grants calendar permissions
3. Google redirects back to: `/workspace/google-calendar/callback?code=...&state=...`

### 4. Backend Processing
The callback endpoint (`/workspace/google-calendar/callback`):

1. **Validates** the OAuth state and code
2. **Exchanges** the code for access/refresh tokens
3. **Fetches** user info from Google Calendar API
4. **Creates/Updates** the `IntegrationUser` record in the database
5. **Redirects** user back to frontend with success/error status

### 5. Frontend Completion
User is redirected to:
- **Success**: `{frontend_url}/integrations?google_calendar=success`
- **Error**: `{frontend_url}/integrations?google_calendar=error`

The frontend can:
- Show a success/error message based on the query parameter
- Refresh the integrations list to show Google Calendar as "Active"

## API Endpoints

### Get OAuth URL
```http
GET /workspace/google-calendar/auth-url
Authorization: Bearer {token}
```

**Response:**
```json
{
  "auth_url": "https://accounts.google.com/oauth/authorize?response_type=code&client_id=...&redirect_uri=...&scope=https://www.googleapis.com/auth/calendar&state=...&code_challenge=...&code_challenge_method=S256"
}
```

### OAuth Callback
```http
GET /workspace/google-calendar/callback?code={code}&state={state}
Authorization: Bearer {token}
```

**Response:**
- **Success**: `307 Redirect` to `{frontend_url}/integrations?google_calendar=success`
- **Error**: `307 Redirect` to `{frontend_url}/integrations?google_calendar=error`

## Configuration Requirements

### Environment Variables
The following environment variables must be set:

```bash
# Google Calendar OAuth Configuration
GOOGLE_CALENDAR_AUTH_URL=https://accounts.google.com/oauth/authorize
GOOGLE_CALENDAR_TOKEN_URL=https://oauth2.googleapis.com/token
GOOGLE_CALENDAR_REDIRECT_URI=http://localhost:8000/workspace/google-calendar/callback

# Frontend URL for redirects
FRONTEND_URL=http://localhost:3000
```

### Database Setup
1. **IntegrationConfig**: Must exist for the organization with `source=GOOGLE_CALENDAR`
2. **IntegrationUser**: Created automatically during OAuth flow

## Security Features

- **PKCE Flow**: Uses Proof Key for Code Exchange for enhanced security
- **State Validation**: Prevents CSRF attacks
- **JWT State**: Stateless OAuth flow with encrypted state
- **Token Refresh**: Automatic token refresh capability

## Error Handling

Common error scenarios:
1. **Missing Configuration**: No Google Calendar config for organization
2. **Invalid Credentials**: Missing or invalid client_id/client_secret
3. **OAuth Errors**: Invalid code, expired state, or user denial
4. **API Errors**: Google Calendar API unavailable

All errors result in redirect to frontend with `google_calendar=error` parameter.

## Testing

Run the Google Calendar router tests:
```bash
poetry run pytest app/workspace/tests/routers/test_google_calendar.py -v
```

## Integration Status

After successful activation, the integration will:
- Move from "Available integrations" to "Active integrations"
- Show "Active" status instead of "Connect" button
- Be available for calendar operations through the integration factory
