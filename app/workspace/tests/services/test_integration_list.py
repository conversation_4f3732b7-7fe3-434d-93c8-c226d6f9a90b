from unittest.mock import AsyncMock
from uuid import uuid4

import pytest

from app.integrations.types import IntegrationSource
from app.workspace.models import IntegrationConfig
from app.workspace.schemas import OrgEnvironment
from app.workspace.services.integration_list import IntegrationListService
from app.workspace.types import EnvironmentType, IntegrationType


@pytest.fixture
def mock_integration_config_service(mocker):
    return mocker.Mock()


@pytest.fixture
def integration_list_service(mock_integration_config_service):
    return IntegrationListService(mock_integration_config_service)


@pytest.fixture
def mock_environment():
    return OrgEnvironment(
        organization_id=uuid4(),
        environment_id=uuid4(),
        type=EnvironmentType.PROD,
    )


@pytest.mark.anyio
async def test_get_integrations_list_with_active_integrations(
    integration_list_service, mock_integration_config_service, mock_environment, mocker
):
    """Test getting integrations list when user has active integrations"""
    user_id = uuid4()

    # Mock configured integrations
    config1_id = uuid4()
    config2_id = uuid4()

    configured_integrations = [
        mocker.Mock(
            spec=IntegrationConfig,
            id=config1_id,
            source=IntegrationSource.SALESFORCE,
            integration_type=IntegrationType.CRM,
        ),
        mocker.Mock(
            spec=IntegrationConfig,
            id=config2_id,
            source=IntegrationSource.GOOGLE_CALENDAR,
            integration_type=IntegrationType.CALENDAR,
        ),
    ]

    mock_integration_config_service.get_integration_configs = AsyncMock(
        return_value=configured_integrations
    )

    # Mock that user has connected to Salesforce but not Google Calendar
    def mock_get_integration_user(config_id, _user_id):
        if config_id == config1_id:  # Salesforce
            return mocker.Mock()  # User has connected
        return None  # User hasn't connected to Google Calendar

    mock_integration_config_service.get_integration_user = AsyncMock(
        side_effect=mock_get_integration_user
    )

    # Call the service
    result = await integration_list_service.get_integrations_list(
        user_id, mock_environment
    )

    # Verify the calls were made
    mock_integration_config_service.get_integration_configs.assert_called_once_with(
        mock_environment
    )

    # Check the result structure
    assert hasattr(result, "active_integrations")
    assert hasattr(result, "available_integrations")

    # Check active integrations (only Salesforce should be active)
    assert len(result.active_integrations) == 1
    active_integration = result.active_integrations[0]
    assert active_integration.source == IntegrationSource.SALESFORCE
    assert active_integration.is_active is True
    assert active_integration.config_id == config1_id

    # Check available integrations (Google Calendar should be available but not active)
    assert len(result.available_integrations) == 1
    available_integration = result.available_integrations[0]
    assert available_integration.source == IntegrationSource.GOOGLE_CALENDAR
    assert available_integration.is_active is False
    assert available_integration.config_id == config2_id


@pytest.mark.anyio
async def test_get_integrations_list_no_configured_integrations(
    integration_list_service, mock_integration_config_service, mock_environment
):
    """Test getting integrations list when no integrations are configured for the organization"""
    user_id = uuid4()

    # Mock no configured integrations
    mock_integration_config_service.get_integration_configs = AsyncMock(return_value=[])

    # Call the service
    result = await integration_list_service.get_integrations_list(
        user_id, mock_environment
    )

    # Check that no integrations are active or available
    assert len(result.active_integrations) == 0
    assert len(result.available_integrations) == 0


def test_get_integration_metadata(integration_list_service):
    """Test that integration metadata is properly defined"""
    metadata = integration_list_service._get_integration_metadata()

    # Check that all integration sources have metadata
    for source in IntegrationSource:
        assert source in metadata
        assert "name" in metadata[source]
        assert "description" in metadata[source]
        assert isinstance(metadata[source]["name"], str)
        assert isinstance(metadata[source]["description"], str)
        assert len(metadata[source]["name"]) > 0
        assert len(metadata[source]["description"]) > 0
