import pytest

from app.integrations.types import IntegrationSource
from app.main import app
from app.workspace.dependencies import get_integration_list_service
from app.workspace.schemas.integration import IntegrationInfo, IntegrationsListResponse
from app.workspace.services.integration_list import IntegrationListService
from app.workspace.types import IntegrationType


@pytest.fixture
def override_integration_list_service(mocker):
    """Override the integration list service dependency"""
    mock_service = mocker.AsyncMock(spec=IntegrationListService)
    app.dependency_overrides[get_integration_list_service] = lambda: mock_service
    yield mock_service
    app.dependency_overrides.pop(get_integration_list_service)


@pytest.mark.anyio
async def test_get_integrations_endpoint(
    async_client, test_app, override_integration_list_service
):
    """Test the GET /integrations endpoint"""
    # Create mock response data
    active_integrations = [
        IntegrationInfo(
            source=IntegrationSource.SALESFORCE,
            integration_type=IntegrationType.CRM,
            name="Salesforce",
            description="Connect your Salesforce CRM to sync leads, contacts, and opportunities",
            is_active=True,
            config_id="123e4567-e89b-12d3-a456-426614174000",
        ),
    ]

    available_integrations = [
        IntegrationInfo(
            source=IntegrationSource.GOOGLE_CALENDAR,
            integration_type=IntegrationType.CALENDAR,
            name="Google Calendar",
            description="Sync your Google Calendar events and meetings",
            is_active=False,
            config_id="123e4567-e89b-12d3-a456-426614174001",
        ),
    ]

    mock_response = IntegrationsListResponse(
        active_integrations=active_integrations,
        available_integrations=available_integrations,
    )

    override_integration_list_service.get_integrations_list.return_value = mock_response

    # Make the request
    url = test_app.url_path_for("get_integrations")
    response = await async_client.get(url)

    # Verify the response
    assert response.status_code == 200
    data = response.json()

    assert "active_integrations" in data
    assert "available_integrations" in data

    assert len(data["active_integrations"]) == 1
    assert len(data["available_integrations"]) == 1

    # Check active integrations
    active = data["active_integrations"]
    assert active[0]["source"] == "salesforce"
    assert active[0]["is_active"] is True
    assert active[0]["config_id"] is not None

    # Check available integrations
    available = data["available_integrations"]
    assert available[0]["source"] == "google_calendar"
    assert available[0]["is_active"] is False
    assert available[0]["config_id"] is not None

    # Verify the service was called with the correct parameters
    override_integration_list_service.get_integrations_list.assert_called_once()
