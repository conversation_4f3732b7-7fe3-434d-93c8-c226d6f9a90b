from fastapi import APIRouter, Query

from app.auth.dependencies import AuthenticatedUserIdDep
from app.common.helpers.logger import get_logger
from app.workspace.dependencies import (
    GoogleCalendarConnectionServiceDep,
    UserEnvDep,
)

logger = get_logger()

router = APIRouter()


@router.get("/google-calendar/auth-url")
async def get_google_calendar_auth_url(
    user_id: AuthenticatedUserIdDep,
    user_env: UserEnvDep,
    service: GoogleCalendarConnectionServiceDep,
):
    oauth_uri = await service.generate_oauth_authorization_uri(
        user_id=user_id,
        environment=user_env,
    )

    return {"auth_url": oauth_uri}


@router.get("/google-calendar/callback")
async def process_google_calendar_callback(
    user_id: AuthenticatedUserIdDep,
    user_env: UserEnvDep,
    service: GoogleCalendarConnectionServiceDep,
    code: str = Query(...),
    state: str = Query(...),
):
    try:
        await service.process_oauth_callback(
            user_id=user_id,
            environment=user_env,
            code=code,
            state=state,
        )

        # Import here to avoid IDE removing unused imports
        from fastapi.responses import RedirectResponse

        from app.core.config import config

        # Redirect to frontend with success message
        frontend_url = f"{config.frontend_url}/integrations?google_calendar=success"
        return RedirectResponse(url=frontend_url)

    except Exception as e:
        logger.error(f"Google Calendar OAuth callback failed: {str(e)}")

        # Import here to avoid IDE removing unused imports
        from fastapi.responses import RedirectResponse

        from app.core.config import config

        # Redirect to frontend with error message
        frontend_url = f"{config.frontend_url}/integrations?google_calendar=error"
        return RedirectResponse(url=frontend_url)
