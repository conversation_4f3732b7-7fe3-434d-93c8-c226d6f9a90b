from uuid import <PERSON>UI<PERSON>

from app.integrations.types import IntegrationSource
from app.workspace.schemas import OrgEnvironment
from app.workspace.schemas.integration import IntegrationInfo, IntegrationsListResponse
from app.workspace.services.integration_config import IntegrationConfigService
from app.workspace.types import SOURCE_TYPE_MAP


class IntegrationListService:
    """Service for listing available and active integrations"""

    def __init__(self, integration_config_service: IntegrationConfigService):
        self.integration_config_service = integration_config_service

    async def get_integrations_list(
        self, user_id: UUID, environment: OrgEnvironment
    ) -> IntegrationsListResponse:
        """Get list of integrations split between active and available"""
        # Get all configured integrations for the organization
        configured_integrations = (
            await self.integration_config_service.get_integration_configs(environment)
        )

        # Get user's active integrations (where user has completed connection)
        user_active_integrations = []
        for config in configured_integrations:
            integration_user = (
                await self.integration_config_service.get_integration_user(
                    config.id, user_id
                )
            )
            if integration_user:
                user_active_integrations.append(config)

        # Create sets for easier lookup
        active_sources = {config.source for config in user_active_integrations}

        # Define integration metadata
        integration_metadata = self._get_integration_metadata()

        active_integrations = []
        available_integrations = []

        # Process configured integrations
        for config in configured_integrations:
            if config.source not in integration_metadata:
                continue

            metadata = integration_metadata[config.source]
            integration_type = SOURCE_TYPE_MAP[config.source]

            integration_info = IntegrationInfo(
                source=config.source,
                integration_type=integration_type,
                name=metadata["name"],
                description=metadata["description"],
                is_active=config.source in active_sources,
                config_id=config.id,
            )

            if config.source in active_sources:
                active_integrations.append(integration_info)
            else:
                available_integrations.append(integration_info)

        return IntegrationsListResponse(
            active_integrations=active_integrations,
            available_integrations=available_integrations,
        )

    def _get_integration_metadata(self) -> dict[IntegrationSource, dict[str, str]]:
        """Get metadata for each integration source"""
        return {
            IntegrationSource.SALESFORCE: {
                "name": "Salesforce",
                "description": "Connect your Salesforce CRM to sync leads, contacts, and opportunities",
            },
            IntegrationSource.HUBSPOT: {
                "name": "HubSpot",
                "description": "Sync your HubSpot contacts and deals with Pearl",
            },
            IntegrationSource.SLACK: {
                "name": "Slack",
                "description": "Connect your Slack workspace for team notifications",
            },
            IntegrationSource.TEAMS: {
                "name": "Microsoft Teams",
                "description": "Connect your Microsoft Teams for team collaboration",
            },
            IntegrationSource.GCS: {
                "name": "Google Cloud Storage",
                "description": "Connect to Google Cloud Storage for file management",
            },
            IntegrationSource.GOOGLE_CALENDAR: {
                "name": "Google Calendar",
                "description": "Sync your Google Calendar events and meetings",
            },
            IntegrationSource.OUTLOOK_CALENDAR: {
                "name": "Outlook Calendar",
                "description": "Connect your Microsoft Outlook calendar",
            },
        }
