from datetime import datetime
from uuid import UUID

from pydantic import BaseModel, ConfigDict

from app.integrations.types import IntegrationSource
from app.workspace.types import IntegrationType


class IntegrationConfigRead(BaseModel):
    id: UUID
    source: IntegrationSource
    integration_type: IntegrationType
    settings: dict
    credentials: dict
    is_active: bool

    model_config = ConfigDict(from_attributes=True)


class IntegrationInfo(BaseModel):
    """Information about an integration type"""

    source: IntegrationSource
    integration_type: IntegrationType
    name: str
    description: str
    is_active: bool
    config_id: UUID | None = None


class IntegrationsListResponse(BaseModel):
    """Response containing active and available integrations"""

    active_integrations: list[IntegrationInfo]
    available_integrations: list[IntegrationInfo]


class BaseCredentials(BaseModel):
    model_config = ConfigDict(extra="forbid")


class BaseSettings(BaseModel):
    model_config = ConfigDict(extra="forbid")


class SalesforceCredentials(BaseCredentials):
    # compatibility with current config
    username: str | None = None
    password: str | None = None
    security_token: str | None = None

    client_id: str | None = None
    client_secret: str | None = None


class SalesforceSettings(BaseSettings):
    pass


class SlackCredentials(BaseCredentials):
    slack_token: str


class SlackSettings(BaseSettings):
    pass


class GoogleCalendarCredentials(BaseCredentials):
    client_id: str | None = None
    client_secret: str | None = None
    token_uri: str = "https://oauth2.googleapis.com/token"
    scopes: list[str] = ["https://www.googleapis.com/auth/calendar"]


class GoogleCalendarSettings(BaseSettings):
    pass


class GoogleCalendarTokenResponse(BaseModel):
    external_user_id: str
    access_token: str
    expires_at: datetime
