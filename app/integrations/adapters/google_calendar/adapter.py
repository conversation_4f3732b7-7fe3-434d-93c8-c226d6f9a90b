import datetime
from typing import Any

from app.common.helpers.logger import get_logger
from app.integrations.adapters.google_calendar.client import GoogleCalendarClient
from app.integrations.adapters.google_calendar.utils import (
    convert_google_calendar_to_calendar,
    convert_google_event_to_calendar_event,
    format_attendee_for_google,
    format_datetime_for_google,
    format_recurrence_for_google,
)
from app.integrations.base.calendar_adapter import BaseCalendarAdapter
from app.integrations.base.credentials_resolver import ICredentials
from app.integrations.schemas import Calendar, CalendarEvent, CalendarEventDateTime
from app.integrations.types import IntegrationSource

logger = get_logger()


class GoogleCalendarAdapter(BaseCalendarAdapter):
    """Google Calendar adapter implementation."""

    def __init__(self, credentials: ICredentials):
        super().__init__(credentials)
        self._client = self._create_client(credentials)

    @property
    def source(self) -> IntegrationSource:
        return IntegrationSource.GOOGLE_CALENDAR

    def _create_client(self, credentials: ICredentials) -> GoogleCalendarClient:
        """Create a Google Calendar client using the provided credentials."""
        required_fields = ["access_token", "refresh_token", "client_id", "client_secret"]
        
        for field in required_fields:
            if field not in credentials.secrets:
                error_msg = f"Google Calendar {field} not found in credentials"
                logger.error(error_msg)
                raise ValueError(error_msg)

        return GoogleCalendarClient(credentials=credentials.secrets)

    async def list_calendars(self) -> list[Calendar]:
        """List all calendars accessible to the user."""
        response = await self._client.list_calendars()
        calendars = []
        
        for calendar_data in response.get("items", []):
            calendar = convert_google_calendar_to_calendar(calendar_data)
            calendars.append(calendar)
        
        return calendars

    async def get_calendar(self, calendar_id: str) -> Calendar:
        """Get a specific calendar by ID."""
        calendar_data = await self._client.get_calendar(calendar_id)
        return convert_google_calendar_to_calendar(calendar_data)

    async def list_events(
        self,
        calendar_id: str,
        start_time: datetime.datetime | None = None,
        end_time: datetime.datetime | None = None,
        max_results: int = 250,
        single_events: bool = True,
        order_by: str = "startTime",
        show_deleted: bool = False,
        page_token: str | None = None,
    ) -> dict[str, Any]:
        """List events from a calendar."""
        time_min = start_time.isoformat() if start_time else None
        time_max = end_time.isoformat() if end_time else None
        
        response = await self._client.list_events(
            calendar_id=calendar_id,
            time_min=time_min,
            time_max=time_max,
            max_results=max_results,
            single_events=single_events,
            order_by=order_by,
            show_deleted=show_deleted,
            page_token=page_token,
        )
        
        events = []
        for event_data in response.get("items", []):
            event = convert_google_event_to_calendar_event(event_data)
            event.calendar_id = calendar_id  # Ensure calendar_id is set
            events.append(event)
        
        return {
            "events": events,
            "next_page_token": response.get("nextPageToken"),
        }

    async def get_event(self, calendar_id: str, event_id: str) -> CalendarEvent:
        """Get a specific event by ID."""
        event_data = await self._client.get_event(calendar_id, event_id)
        event = convert_google_event_to_calendar_event(event_data)
        event.calendar_id = calendar_id
        return event

    async def create_event(
        self,
        calendar_id: str,
        title: str,
        start: CalendarEventDateTime,
        end: CalendarEventDateTime,
        description: str | None = None,
        location: str | None = None,
        attendees: list[dict[str, Any]] | None = None,
        all_day: bool = False,
        send_notifications: bool = True,
        **kwargs: Any,
    ) -> CalendarEvent:
        """Create a new event in the calendar."""
        event_data = {
            "summary": title,
            "start": format_datetime_for_google(start, all_day),
            "end": format_datetime_for_google(end, all_day),
        }
        
        if description:
            event_data["description"] = description
        
        if location:
            event_data["location"] = location
        
        if attendees:
            # Convert attendee dicts to Google format
            google_attendees = []
            for attendee in attendees:
                if isinstance(attendee, dict):
                    google_attendees.append(attendee)
                else:
                    google_attendees.append(format_attendee_for_google(attendee))
            event_data["attendees"] = google_attendees
        
        # Handle additional kwargs
        if "recurrence" in kwargs and kwargs["recurrence"]:
            event_data["recurrence"] = format_recurrence_for_google(kwargs["recurrence"])
        
        if "visibility" in kwargs:
            event_data["visibility"] = kwargs["visibility"]
        
        response = await self._client.create_event(
            calendar_id=calendar_id,
            event_data=event_data,
            send_notifications=send_notifications,
        )
        
        event = convert_google_event_to_calendar_event(response)
        event.calendar_id = calendar_id
        return event

    async def update_event(
        self,
        calendar_id: str,
        event_id: str,
        title: str | None = None,
        start: CalendarEventDateTime | None = None,
        end: CalendarEventDateTime | None = None,
        description: str | None = None,
        location: str | None = None,
        attendees: list[dict[str, Any]] | None = None,
        all_day: bool | None = None,
        status: str | None = None,
        send_notifications: bool = True,
        **kwargs: Any,
    ) -> CalendarEvent:
        """Update an existing event."""
        # Get current event to merge changes
        current_event_data = await self._client.get_event(calendar_id, event_id)
        
        # Update only provided fields
        if title is not None:
            current_event_data["summary"] = title
        
        if start is not None:
            current_event_data["start"] = format_datetime_for_google(start, all_day or False)
        
        if end is not None:
            current_event_data["end"] = format_datetime_for_google(end, all_day or False)
        
        if description is not None:
            current_event_data["description"] = description
        
        if location is not None:
            current_event_data["location"] = location
        
        if attendees is not None:
            google_attendees = []
            for attendee in attendees:
                if isinstance(attendee, dict):
                    google_attendees.append(attendee)
                else:
                    google_attendees.append(format_attendee_for_google(attendee))
            current_event_data["attendees"] = google_attendees
        
        if status is not None:
            current_event_data["status"] = status
        
        # Handle additional kwargs
        if "recurrence" in kwargs and kwargs["recurrence"]:
            current_event_data["recurrence"] = format_recurrence_for_google(kwargs["recurrence"])
        
        if "visibility" in kwargs:
            current_event_data["visibility"] = kwargs["visibility"]
        
        response = await self._client.update_event(
            calendar_id=calendar_id,
            event_id=event_id,
            event_data=current_event_data,
            send_notifications=send_notifications,
        )
        
        event = convert_google_event_to_calendar_event(response)
        event.calendar_id = calendar_id
        return event

    async def delete_event(
        self,
        calendar_id: str,
        event_id: str,
        send_notifications: bool = True,
    ) -> bool:
        """Delete an event from the calendar."""
        try:
            await self._client.delete_event(
                calendar_id=calendar_id,
                event_id=event_id,
                send_notifications=send_notifications,
            )
            return True
        except Exception as e:
            logger.error(f"Failed to delete event {event_id}: {str(e)}")
            return False

    async def get_free_busy(
        self,
        calendar_ids: list[str],
        start_time: datetime.datetime,
        end_time: datetime.datetime,
        timezone: str | None = None,
    ) -> dict[str, Any]:
        """Get free/busy information for calendars."""
        time_min = start_time.isoformat()
        time_max = end_time.isoformat()
        
        response = await self._client.get_free_busy(
            calendar_ids=calendar_ids,
            time_min=time_min,
            time_max=time_max,
            timezone=timezone,
        )
        
        # Convert response to our format
        calendars = []
        for cal_id in calendar_ids:
            cal_data = response.get("calendars", {}).get(cal_id, {})
            busy_periods = []
            
            for busy in cal_data.get("busy", []):
                start_dt = datetime.datetime.fromisoformat(busy["start"].replace("Z", "+00:00"))
                end_dt = datetime.datetime.fromisoformat(busy["end"].replace("Z", "+00:00"))
                busy_periods.append({"start": start_dt, "end": end_dt})
            
            calendars.append({
                "calendar_id": cal_id,
                "busy_periods": busy_periods,
                "errors": cal_data.get("errors", []),
            })
        
        return {
            "calendars": calendars,
            "start_time": start_time,
            "end_time": end_time,
        }

    async def search_events(
        self,
        calendar_id: str,
        query: str,
        start_time: datetime.datetime | None = None,
        end_time: datetime.datetime | None = None,
        max_results: int = 250,
        order_by: str = "startTime",
    ) -> list[CalendarEvent]:
        """Search for events in a calendar."""
        # Google Calendar API doesn't have a direct search endpoint
        # We'll list events and filter by query
        events_response = await self.list_events(
            calendar_id=calendar_id,
            start_time=start_time,
            end_time=end_time,
            max_results=max_results,
            order_by=order_by,
        )
        
        events = events_response["events"]
        query_lower = query.lower()
        
        # Filter events by query in title, description, or location
        filtered_events = []
        for event in events:
            if (
                query_lower in event.title.lower()
                or (event.description and query_lower in event.description.lower())
                or (event.location and query_lower in event.location.lower())
            ):
                filtered_events.append(event)
        
        return filtered_events

    async def get_user_info(self) -> dict[str, Any]:
        """Get information about the authenticated user."""
        return await self._client.get_user_info()
