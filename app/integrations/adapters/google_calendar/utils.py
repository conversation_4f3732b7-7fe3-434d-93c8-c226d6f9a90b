import datetime
from typing import Any

from app.integrations.schemas import (
    Calendar,
    CalendarEvent,
    CalendarEventAttendee,
    CalendarEventDateTime,
    CalendarEventRecurrence,
)


def parse_google_datetime(dt_data: dict[str, Any] | None) -> CalendarEventDateTime | None:
    """Parse Google Calendar datetime format to our schema."""
    if not dt_data:
        return None
    
    # Google Calendar uses either 'dateTime' for timed events or 'date' for all-day events
    dt_str = dt_data.get("dateTime")
    date_str = dt_data.get("date")
    timezone = dt_data.get("timeZone")
    
    if dt_str:
        # Parse ISO format datetime
        dt = datetime.datetime.fromisoformat(dt_str.replace("Z", "+00:00"))
        return CalendarEventDateTime(dt=dt, timezone=timezone)
    elif date_str:
        # Parse date-only format
        date = datetime.datetime.strptime(date_str, "%Y-%m-%d").date()
        return CalendarEventDateTime(date=date, timezone=timezone)
    
    return None


def format_datetime_for_google(dt: CalendarEventDateTime, all_day: bool = False) -> dict[str, Any]:
    """Format our datetime schema to Google Calendar format."""
    if all_day and dt.date:
        return {"date": dt.date.isoformat()}
    elif dt.dt:
        result = {"dateTime": dt.dt.isoformat()}
        if dt.timezone:
            result["timeZone"] = dt.timezone
        return result
    elif dt.date:
        # Convert date to datetime for timed events
        dt_obj = datetime.datetime.combine(dt.date, datetime.time.min)
        result = {"dateTime": dt_obj.isoformat()}
        if dt.timezone:
            result["timeZone"] = dt.timezone
        return result
    
    raise ValueError("Invalid datetime data")


def parse_google_attendee(attendee_data: dict[str, Any]) -> CalendarEventAttendee:
    """Parse Google Calendar attendee format to our schema."""
    return CalendarEventAttendee(
        email=attendee_data.get("email", ""),
        name=attendee_data.get("displayName"),
        response_status=attendee_data.get("responseStatus"),
        is_organizer=attendee_data.get("organizer", False),
        is_optional=attendee_data.get("optional", False),
    )


def format_attendee_for_google(attendee: CalendarEventAttendee) -> dict[str, Any]:
    """Format our attendee schema to Google Calendar format."""
    result = {"email": attendee.email}
    
    if attendee.name:
        result["displayName"] = attendee.name
    if attendee.response_status:
        result["responseStatus"] = attendee.response_status
    if attendee.is_organizer:
        result["organizer"] = True
    if attendee.is_optional:
        result["optional"] = True
        
    return result


def parse_google_recurrence(recurrence_data: list[str] | None) -> CalendarEventRecurrence | None:
    """Parse Google Calendar recurrence rules to our schema."""
    if not recurrence_data:
        return None
    
    # Google Calendar uses RRULE format
    # This is a simplified parser - full RRULE parsing would be more complex
    rrule = recurrence_data[0] if recurrence_data else ""
    
    if not rrule.startswith("RRULE:"):
        return None
    
    # Parse basic RRULE components
    parts = rrule[6:].split(";")  # Remove "RRULE:" prefix
    rule_dict = {}
    
    for part in parts:
        if "=" in part:
            key, value = part.split("=", 1)
            rule_dict[key] = value
    
    frequency_map = {
        "DAILY": "daily",
        "WEEKLY": "weekly", 
        "MONTHLY": "monthly",
        "YEARLY": "yearly",
    }
    
    frequency = frequency_map.get(rule_dict.get("FREQ", ""), "daily")
    interval = int(rule_dict.get("INTERVAL", 1))
    count = int(rule_dict["COUNT"]) if "COUNT" in rule_dict else None
    
    until = None
    if "UNTIL" in rule_dict:
        until_str = rule_dict["UNTIL"]
        try:
            until = datetime.datetime.strptime(until_str, "%Y%m%dT%H%M%SZ")
        except ValueError:
            try:
                until = datetime.datetime.strptime(until_str, "%Y%m%d")
            except ValueError:
                pass
    
    by_day = None
    if "BYDAY" in rule_dict:
        by_day = rule_dict["BYDAY"].split(",")
    
    return CalendarEventRecurrence(
        frequency=frequency,
        interval=interval,
        count=count,
        until=until,
        by_day=by_day,
    )


def format_recurrence_for_google(recurrence: CalendarEventRecurrence) -> list[str]:
    """Format our recurrence schema to Google Calendar RRULE format."""
    frequency_map = {
        "daily": "DAILY",
        "weekly": "WEEKLY",
        "monthly": "MONTHLY", 
        "yearly": "YEARLY",
    }
    
    rrule_parts = [f"FREQ={frequency_map[recurrence.frequency]}"]
    
    if recurrence.interval > 1:
        rrule_parts.append(f"INTERVAL={recurrence.interval}")
    
    if recurrence.count:
        rrule_parts.append(f"COUNT={recurrence.count}")
    
    if recurrence.until:
        until_str = recurrence.until.strftime("%Y%m%dT%H%M%SZ")
        rrule_parts.append(f"UNTIL={until_str}")
    
    if recurrence.by_day:
        rrule_parts.append(f"BYDAY={','.join(recurrence.by_day)}")
    
    return [f"RRULE:{';'.join(rrule_parts)}"]


def convert_google_event_to_calendar_event(event_data: dict[str, Any]) -> CalendarEvent:
    """Convert Google Calendar event to our CalendarEvent schema."""
    start = parse_google_datetime(event_data.get("start"))
    end = parse_google_datetime(event_data.get("end"))
    
    attendees = []
    if "attendees" in event_data:
        attendees = [parse_google_attendee(att) for att in event_data["attendees"]]
    
    organizer = None
    if "organizer" in event_data:
        organizer = parse_google_attendee(event_data["organizer"])
    
    recurrence = parse_google_recurrence(event_data.get("recurrence"))
    
    # Determine if it's an all-day event
    all_day = start and start.date is not None and start.dt is None
    
    created_at = None
    if "created" in event_data:
        created_at = datetime.datetime.fromisoformat(
            event_data["created"].replace("Z", "+00:00")
        )
    
    updated_at = None
    if "updated" in event_data:
        updated_at = datetime.datetime.fromisoformat(
            event_data["updated"].replace("Z", "+00:00")
        )
    
    return CalendarEvent(
        id=event_data["id"],
        calendar_id=event_data.get("organizer", {}).get("email", ""),
        title=event_data.get("summary", ""),
        description=event_data.get("description"),
        location=event_data.get("location"),
        start=start,
        end=end,
        all_day=all_day,
        attendees=attendees,
        organizer=organizer,
        recurrence=recurrence,
        status=event_data.get("status", "confirmed"),
        visibility=event_data.get("visibility", "default"),
        created_at=created_at,
        updated_at=updated_at,
        html_link=event_data.get("htmlLink"),
        meeting_url=event_data.get("hangoutLink"),
    )


def convert_google_calendar_to_calendar(calendar_data: dict[str, Any]) -> Calendar:
    """Convert Google Calendar to our Calendar schema."""
    return Calendar(
        id=calendar_data["id"],
        name=calendar_data.get("summary", ""),
        description=calendar_data.get("description"),
        timezone=calendar_data.get("timeZone"),
        is_primary=calendar_data.get("primary", False),
        access_role=calendar_data.get("accessRole", "reader"),
        color_id=calendar_data.get("colorId"),
        background_color=calendar_data.get("backgroundColor"),
        foreground_color=calendar_data.get("foregroundColor"),
    )
