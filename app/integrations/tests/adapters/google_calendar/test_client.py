import datetime
from unittest.mock import AsyncMock, Mock, patch

import pytest

from app.integrations.adapters.google_calendar.client import (
    GoogleCalendarClient,
    GoogleCalendarClientError,
)


@pytest.fixture
def mock_credentials():
    return {
        "access_token": "test_access_token",
        "refresh_token": "test_refresh_token",
        "client_id": "test_client_id",
        "client_secret": "test_client_secret",
        "token_uri": "https://oauth2.googleapis.com/token",
        "scopes": ["https://www.googleapis.com/auth/calendar"],
    }


@pytest.fixture
def mock_service():
    service = Mock()
    
    # Mock calendar list
    calendar_list = Mock()
    calendar_list.list.return_value.execute.return_value = {
        "items": [
            {
                "id": "primary",
                "summary": "Test Calendar",
                "primary": True,
                "accessRole": "owner",
            }
        ]
    }
    calendar_list.get.return_value.execute.return_value = {
        "id": "primary",
        "summary": "Test Calendar",
        "primary": True,
        "accessRole": "owner",
    }
    service.calendarList.return_value = calendar_list
    
    # Mock events
    events = Mock()
    events.list.return_value.execute.return_value = {
        "items": [
            {
                "id": "event1",
                "summary": "Test Event",
                "start": {"dateTime": "2024-01-01T10:00:00Z"},
                "end": {"dateTime": "2024-01-01T11:00:00Z"},
                "status": "confirmed",
            }
        ],
        "nextPageToken": None,
    }
    events.get.return_value.execute.return_value = {
        "id": "event1",
        "summary": "Test Event",
        "start": {"dateTime": "2024-01-01T10:00:00Z"},
        "end": {"dateTime": "2024-01-01T11:00:00Z"},
        "status": "confirmed",
    }
    events.insert.return_value.execute.return_value = {
        "id": "new_event",
        "summary": "New Event",
        "start": {"dateTime": "2024-01-01T10:00:00Z"},
        "end": {"dateTime": "2024-01-01T11:00:00Z"},
        "status": "confirmed",
    }
    events.update.return_value.execute.return_value = {
        "id": "event1",
        "summary": "Updated Event",
        "start": {"dateTime": "2024-01-01T10:00:00Z"},
        "end": {"dateTime": "2024-01-01T11:00:00Z"},
        "status": "confirmed",
    }
    events.delete.return_value.execute.return_value = None
    service.events.return_value = events
    
    # Mock freebusy
    freebusy = Mock()
    freebusy.query.return_value.execute.return_value = {
        "calendars": {
            "primary": {
                "busy": [
                    {
                        "start": "2024-01-01T10:00:00Z",
                        "end": "2024-01-01T11:00:00Z",
                    }
                ]
            }
        }
    }
    service.freebusy.return_value = freebusy
    
    return service


@pytest.fixture
def google_calendar_client(mock_credentials, mock_service):
    with patch("app.integrations.adapters.google_calendar.client.build", return_value=mock_service):
        with patch("app.integrations.adapters.google_calendar.client.OAuth2Credentials"):
            return GoogleCalendarClient(mock_credentials)


class TestGoogleCalendarClient:
    def test_init_success(self, mock_credentials, mock_service):
        with patch("app.integrations.adapters.google_calendar.client.build", return_value=mock_service):
            with patch("app.integrations.adapters.google_calendar.client.OAuth2Credentials") as mock_creds:
                client = GoogleCalendarClient(mock_credentials)
                assert client._service == mock_service
                mock_creds.assert_called_once()

    def test_init_missing_credentials(self):
        incomplete_credentials = {"access_token": "test"}
        
        with pytest.raises(GoogleCalendarClientError):
            GoogleCalendarClient(incomplete_credentials)

    @pytest.mark.asyncio
    async def test_list_calendars(self, google_calendar_client):
        result = await google_calendar_client.list_calendars()
        
        assert "items" in result
        assert len(result["items"]) == 1
        assert result["items"][0]["id"] == "primary"

    @pytest.mark.asyncio
    async def test_get_calendar(self, google_calendar_client):
        result = await google_calendar_client.get_calendar("primary")
        
        assert result["id"] == "primary"
        assert result["summary"] == "Test Calendar"

    @pytest.mark.asyncio
    async def test_list_events(self, google_calendar_client):
        result = await google_calendar_client.list_events("primary")
        
        assert "items" in result
        assert len(result["items"]) == 1
        assert result["items"][0]["id"] == "event1"

    @pytest.mark.asyncio
    async def test_get_event(self, google_calendar_client):
        result = await google_calendar_client.get_event("primary", "event1")
        
        assert result["id"] == "event1"
        assert result["summary"] == "Test Event"

    @pytest.mark.asyncio
    async def test_create_event(self, google_calendar_client):
        event_data = {
            "summary": "New Event",
            "start": {"dateTime": "2024-01-01T10:00:00Z"},
            "end": {"dateTime": "2024-01-01T11:00:00Z"},
        }
        
        result = await google_calendar_client.create_event("primary", event_data)
        
        assert result["id"] == "new_event"
        assert result["summary"] == "New Event"

    @pytest.mark.asyncio
    async def test_update_event(self, google_calendar_client):
        event_data = {
            "summary": "Updated Event",
            "start": {"dateTime": "2024-01-01T10:00:00Z"},
            "end": {"dateTime": "2024-01-01T11:00:00Z"},
        }
        
        result = await google_calendar_client.update_event("primary", "event1", event_data)
        
        assert result["id"] == "event1"
        assert result["summary"] == "Updated Event"

    @pytest.mark.asyncio
    async def test_delete_event(self, google_calendar_client):
        # Should not raise an exception
        await google_calendar_client.delete_event("primary", "event1")

    @pytest.mark.asyncio
    async def test_get_free_busy(self, google_calendar_client):
        result = await google_calendar_client.get_free_busy(
            ["primary"],
            "2024-01-01T00:00:00Z",
            "2024-01-01T23:59:59Z"
        )
        
        assert "calendars" in result
        assert "primary" in result["calendars"]
        assert len(result["calendars"]["primary"]["busy"]) == 1

    @pytest.mark.asyncio
    async def test_get_user_info(self, google_calendar_client):
        result = await google_calendar_client.get_user_info()
        
        assert result["id"] == "primary"
        assert result["summary"] == "Test Calendar"
